<?php

use yii\helpers\Url;
use common\models\DocumentType;
use common\models\MenuItem;
use yii\caching\TagDependency;

$location = Yii::$app->id === 'app-frontend' ? 'frontend' : 'backend';
$userId = Yii::$app->user->id;

$cacheKey = 'sidebar-menu-user-' . ($userId ?: 'guest') . '-' . $location;

$menuItems = Yii::$app->cache->get($cacheKey);

if ($menuItems === false) {
    $menuItems = [];

    if (!Yii::$app->user->isGuest) {
        $rawMenuItems = MenuItem::find()
            ->where(['visible' => 1])
            ->andWhere(['or', ['location' => $location], ['location' => 'both']])
            ->orderBy(['sort_order' => SORT_ASC])
            ->asArray()
            ->all();

        $menuItems = buildMenuTree($rawMenuItems);

        // Add "Nieuw Rapport" section for frontend
        if ($location === 'frontend') {
            $menuItems[] = [
                'label' => 'Nieuw Rapport',
                'header' => true,
            ];

            $currentRoute = Yii::$app->controller->route;
            $currentDocId = Yii::$app->request->get('document_id');
            $documents = DocumentType::find()->all();

            foreach ($documents as $document) {
                $menuItems[] = [
                    'label' => $document->type,
                    'url' => ['/document/create', 'document_id' => $document->id],
                    'icon' => 'file-plus',
                    'iconType' => 'lucide',
                    'active' => ($currentRoute === 'document/create' && $currentDocId == $document->id),
                ];
            }
        }

        // ... optionally reinstate your role functionality section here ...

    } else {
        // Guest users
        $menuItems[] = [
            'label' => 'Login',
            'url' => ['/site/login'],
            'icon' => 'log-in',
            'iconType' => 'lucide',
        ];
    }

    Yii::$app->cache->set($cacheKey, $menuItems, 3600, new TagDependency(['tags' => ['menu', 'user:' . $userId]]));
}
?>
<aside class="main-sidebar bg-white elevation-4 rounded-end-3">
    <!-- Brand Logo -->
    <a href="<?= Url::home() ?>" class="brand-link text-decoration-none text-center">
        <span class="brand-text fw-semibold"><?= Yii::$app->name ?></span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">
        <!-- Sidebar Menu -->
        <nav class="mt-4">
            <?= \common\components\LucideSidebarMenuHelper::widget([
                'items' => $menuItems
            ]) ?>
        </nav>
    </div>
</aside>