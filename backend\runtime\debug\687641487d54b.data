a:14:{s:6:"config";s:15217:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:68:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}}}";s:3:"log";s:16678:"a:1:{s:8:"messages";a:38:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752580424.384974;i:4;a:0:{}i:5;i:2875832;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752580424.387876;i:4;a:0:{}i:5;i:2979776;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752580424.387901;i:4;a:0:{}i:5;i:2980072;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752580424.389975;i:4;a:0:{}i:5;i:3010264;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752580424.391533;i:4;a:0:{}i:5;i:3037736;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752580424.395245;i:4;a:0:{}i:5;i:3191496;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752580424.395282;i:4;a:0:{}i:5;i:3192136;}i:7;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.444978;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5628112;}i:8;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1752580424.445059;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5695768;}i:13;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.476529;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5753744;}i:16;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.479072;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5778240;}i:19;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.489721;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6157456;}i:22;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1752580424.495076;i:4;a:0:{}i:5;i:6376232;}i:23;a:6:{i:0;s:39:"User '1' logged in from ::1 via cookie.";i:1;i:4;i:2;s:27:"yii\web\User::loginByCookie";i:3;d:1752580424.498465;i:4;a:0:{}i:5;i:6487184;}i:24;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752580424.512202;i:4;a:0:{}i:5;i:6937608;}i:25;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752580424.514599;i:4;a:0:{}i:5;i:7052168;}i:26;a:6:{i:0;s:21:"Loading module: audit";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752580424.514625;i:4;a:0:{}i:5;i:7052808;}i:27;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.516897;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7122728;}i:30;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.519003;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7134360;}i:33;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.520147;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7134784;}i:36;a:6:{i:0;s:40:"Bootstrap with bedezign\yii2\audit\Audit";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752580424.539771;i:4;a:0:{}i:5;i:7380112;}i:38;a:6:{i:0;s:35:"Route requested: 'audit/entry/view'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1752580424.539895;i:4;a:0:{}i:5;i:7379048;}i:39;a:6:{i:0;s:30:"Route to run: audit/entry/view";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1752580424.549631;i:4;a:0:{}i:5;i:7459072;}i:40;a:6:{i:0;s:77:"Running action: bedezign\yii2\audit\controllers\EntryController::actionView()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1752580424.583559;i:4;a:0:{}i:5;i:7665744;}i:41;a:6:{i:0;s:44:"SELECT * FROM `audit_entry` WHERE `id`='947'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.58379;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:87;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7673856;}i:44;a:6:{i:0;s:47:"SELECT * FROM `audit_data` WHERE `entry_id`=947";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.620566;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7829600;}i:47;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_data`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.622644;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7848576;}i:50;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.626706;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7857568;}i:53;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_data' AND `kcu`.`TABLE_NAME` = 'audit_data'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.627737;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7860352;}i:56;a:6:{i:0;s:54:"SELECT COUNT(*) FROM `audit_mail` WHERE `entry_id`=947";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.647305;i:4;a:3:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\MailPanel.php";s:4:"line";i:114;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:36:"bedezign\yii2\audit\panels\MailPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8127416;}i:59;a:6:{i:0;s:55:"SELECT COUNT(*) FROM `audit_trail` WHERE `entry_id`=947";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.663151;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\TrailPanel.php";s:4:"line";i:33;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:37:"bedezign\yii2\audit\panels\TrailPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8158936;}i:62;a:6:{i:0;s:60:"SELECT COUNT(*) FROM `audit_javascript` WHERE `entry_id`=947";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.676747;i:4;a:3:{i:0;a:5:{s:4:"file";s:77:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\JavascriptPanel.php";s:4:"line";i:41;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:42:"bedezign\yii2\audit\panels\JavascriptPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8181952;}i:65;a:6:{i:0;s:55:"SELECT COUNT(*) FROM `audit_error` WHERE `entry_id`=947";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.68833;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\ErrorPanel.php";s:4:"line";i:102;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:37:"bedezign\yii2\audit\panels\ErrorPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8207872;}i:68;a:6:{i:0;s:92:"Rendering view file: C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\views\entry\view.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752580424.696391;i:4;a:1:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:59;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:8663176;}i:69;a:6:{i:0;s:106:"Rendering view file: C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src/views/default/panels/log/detail.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752580424.776109;i:4;a:3:{i:0;a:5:{s:4:"file";s:70:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\LogPanel.php";s:4:"line";i:35;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\views\entry\view.php";s:4:"line";i:100;s:8:"function";s:9:"getDetail";s:5:"class";s:35:"bedezign\yii2\audit\panels\LogPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:59;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9528600;}i:70;a:6:{i:0;s:94:"Rendering view file: C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752580424.794426;i:4;a:1:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:59;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9847144;}i:71;a:6:{i:0;s:97:"Rendering view file: C:\Web\Reclassering\vendor\bedezign\yii2-audit\src/views/_audit_entry_id.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752580424.867421;i:4;a:2:{i:0;a:5:{s:4:"file";s:73:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\views\layouts\main.php";s:4:"line";i:78;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:59;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10070504;}i:72;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='audit/entry/view'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.879177;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:10087640;}}}";s:9:"profiling";s:23608:"a:3:{s:6:"memory";i:10219104;s:4:"time";d:0.5208110809326172;s:8:"messages";a:36:{i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1752580424.445076;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5696736;}i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1752580424.472888;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5740040;}i:11;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.47295;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5739824;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.476422;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5752456;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.476554;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5754656;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.477519;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5757232;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.479142;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5779600;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.481586;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5782128;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.489809;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6158480;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.490708;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6160848;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.51694;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7123640;}i:29;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.51891;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7133072;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.51903;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7135272;}i:32;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.519918;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7137184;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.52018;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7135824;}i:35;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.522668;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7137680;}i:42;a:6:{i:0;s:44:"SELECT * FROM `audit_entry` WHERE `id`='947'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.583822;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:87;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7675360;}i:43;a:6:{i:0;s:44:"SELECT * FROM `audit_entry` WHERE `id`='947'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.584982;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:87;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7677696;}i:45;a:6:{i:0;s:47:"SELECT * FROM `audit_data` WHERE `entry_id`=947";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.620629;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7830784;}i:46;a:6:{i:0;s:47:"SELECT * FROM `audit_data` WHERE `entry_id`=947";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.622472;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7844744;}i:48;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_data`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.622672;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7849864;}i:49;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_data`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.626597;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7855904;}i:51;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.626732;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7858856;}i:52;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.627524;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7860952;}i:54;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_data' AND `kcu`.`TABLE_NAME` = 'audit_data'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.627773;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7861768;}i:55;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_data' AND `kcu`.`TABLE_NAME` = 'audit_data'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.630129;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7864696;}i:57;a:6:{i:0;s:54:"SELECT COUNT(*) FROM `audit_mail` WHERE `entry_id`=947";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.647364;i:4;a:3:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\MailPanel.php";s:4:"line";i:114;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:36:"bedezign\yii2\audit\panels\MailPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8128976;}i:58;a:6:{i:0;s:54:"SELECT COUNT(*) FROM `audit_mail` WHERE `entry_id`=947";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.648834;i:4;a:3:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\MailPanel.php";s:4:"line";i:114;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:36:"bedezign\yii2\audit\panels\MailPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8130696;}i:60;a:6:{i:0;s:55:"SELECT COUNT(*) FROM `audit_trail` WHERE `entry_id`=947";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.663191;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\TrailPanel.php";s:4:"line";i:33;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:37:"bedezign\yii2\audit\panels\TrailPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8160496;}i:61;a:6:{i:0;s:55:"SELECT COUNT(*) FROM `audit_trail` WHERE `entry_id`=947";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.664769;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\TrailPanel.php";s:4:"line";i:33;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:37:"bedezign\yii2\audit\panels\TrailPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8162176;}i:63;a:6:{i:0;s:60:"SELECT COUNT(*) FROM `audit_javascript` WHERE `entry_id`=947";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.676834;i:4;a:3:{i:0;a:5:{s:4:"file";s:77:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\JavascriptPanel.php";s:4:"line";i:41;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:42:"bedezign\yii2\audit\panels\JavascriptPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8183512;}i:64;a:6:{i:0;s:60:"SELECT COUNT(*) FROM `audit_javascript` WHERE `entry_id`=947";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.678514;i:4;a:3:{i:0;a:5:{s:4:"file";s:77:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\JavascriptPanel.php";s:4:"line";i:41;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:42:"bedezign\yii2\audit\panels\JavascriptPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8185192;}i:66;a:6:{i:0;s:55:"SELECT COUNT(*) FROM `audit_error` WHERE `entry_id`=947";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.688403;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\ErrorPanel.php";s:4:"line";i:102;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:37:"bedezign\yii2\audit\panels\ErrorPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8209432;}i:67;a:6:{i:0;s:55:"SELECT COUNT(*) FROM `audit_error` WHERE `entry_id`=947";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.690706;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\ErrorPanel.php";s:4:"line";i:102;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:37:"bedezign\yii2\audit\panels\ErrorPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8211112;}i:73;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='audit/entry/view'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.879219;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:10088768;}i:74;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='audit/entry/view'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.880177;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:10089920;}}}";s:2:"db";s:22839:"a:1:{s:8:"messages";a:34:{i:11;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.47295;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5739824;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.476422;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5752456;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.476554;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5754656;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.477519;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5757232;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.479142;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5779600;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.481586;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5782128;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.489809;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6158480;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.490708;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6160848;}i:28;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.51694;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7123640;}i:29;a:6:{i:0;s:36:"SHOW FULL COLUMNS FROM `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.51891;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7133072;}i:31;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.51903;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7135272;}i:32;a:6:{i:0;s:31:"SHOW CREATE TABLE `audit_entry`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.519918;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7137184;}i:34;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.52018;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7135824;}i:35;a:6:{i:0;s:778:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_entry' AND `kcu`.`TABLE_NAME` = 'audit_entry'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.522668;i:4;a:1:{i:0;a:5:{s:4:"file";s:60:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\Audit.php";s:4:"line";i:236;s:8:"function";s:14:"getTableSchema";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}}i:5;i:7137680;}i:42;a:6:{i:0;s:44:"SELECT * FROM `audit_entry` WHERE `id`='947'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.583822;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:87;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7675360;}i:43;a:6:{i:0;s:44:"SELECT * FROM `audit_entry` WHERE `id`='947'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.584982;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:87;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7677696;}i:45;a:6:{i:0;s:47:"SELECT * FROM `audit_data` WHERE `entry_id`=947";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.620629;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7830784;}i:46;a:6:{i:0;s:47:"SELECT * FROM `audit_data` WHERE `entry_id`=947";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.622472;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7844744;}i:48;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_data`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.622672;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7849864;}i:49;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `audit_data`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.626597;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7855904;}i:51;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.626732;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7858856;}i:52;a:6:{i:0;s:30:"SHOW CREATE TABLE `audit_data`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.627524;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7860952;}i:54;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_data' AND `kcu`.`TABLE_NAME` = 'audit_data'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.627773;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7861768;}i:55;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'audit_data' AND `kcu`.`TABLE_NAME` = 'audit_data'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.630129;i:4;a:2:{i:0;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:99;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:7864696;}i:57;a:6:{i:0;s:54:"SELECT COUNT(*) FROM `audit_mail` WHERE `entry_id`=947";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.647364;i:4;a:3:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\MailPanel.php";s:4:"line";i:114;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:36:"bedezign\yii2\audit\panels\MailPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8128976;}i:58;a:6:{i:0;s:54:"SELECT COUNT(*) FROM `audit_mail` WHERE `entry_id`=947";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.648834;i:4;a:3:{i:0;a:5:{s:4:"file";s:71:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\MailPanel.php";s:4:"line";i:114;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:36:"bedezign\yii2\audit\panels\MailPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8130696;}i:60;a:6:{i:0;s:55:"SELECT COUNT(*) FROM `audit_trail` WHERE `entry_id`=947";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.663191;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\TrailPanel.php";s:4:"line";i:33;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:37:"bedezign\yii2\audit\panels\TrailPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8160496;}i:61;a:6:{i:0;s:55:"SELECT COUNT(*) FROM `audit_trail` WHERE `entry_id`=947";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.664769;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\TrailPanel.php";s:4:"line";i:33;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:37:"bedezign\yii2\audit\panels\TrailPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8162176;}i:63;a:6:{i:0;s:60:"SELECT COUNT(*) FROM `audit_javascript` WHERE `entry_id`=947";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.676834;i:4;a:3:{i:0;a:5:{s:4:"file";s:77:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\JavascriptPanel.php";s:4:"line";i:41;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:42:"bedezign\yii2\audit\panels\JavascriptPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8183512;}i:64;a:6:{i:0;s:60:"SELECT COUNT(*) FROM `audit_javascript` WHERE `entry_id`=947";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.678514;i:4;a:3:{i:0;a:5:{s:4:"file";s:77:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\JavascriptPanel.php";s:4:"line";i:41;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:42:"bedezign\yii2\audit\panels\JavascriptPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8185192;}i:66;a:6:{i:0;s:55:"SELECT COUNT(*) FROM `audit_error` WHERE `entry_id`=947";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.688403;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\ErrorPanel.php";s:4:"line";i:102;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:37:"bedezign\yii2\audit\panels\ErrorPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8209432;}i:67;a:6:{i:0;s:55:"SELECT COUNT(*) FROM `audit_error` WHERE `entry_id`=947";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.690706;i:4;a:3:{i:0;a:5:{s:4:"file";s:72:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\panels\ErrorPanel.php";s:4:"line";i:102;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:101;s:8:"function";s:12:"hasEntryData";s:5:"class";s:37:"bedezign\yii2\audit\panels\ErrorPanel";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:82:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src\controllers\EntryController.php";s:4:"line";i:48;s:8:"function";s:8:"loadData";s:5:"class";s:47:"bedezign\yii2\audit\controllers\EntryController";s:4:"type";s:2:"->";}}i:5;i:8211112;}i:73;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='audit/entry/view'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.879219;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:10088768;}i:74;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='audit/entry/view'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752580424.880177;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:10089920;}}}";s:5:"event";s:16316:"a:90:{i:0;a:5:{s:4:"time";d:1752580424.435615;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1752580424.472867;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1752580424.491543;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:1752580424.491604;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:1752580424.491829;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:5;a:5:{s:4:"time";d:1752580424.498517;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:6;a:5:{s:4:"time";d:1752580424.504727;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:7;a:5:{s:4:"time";d:1752580424.53985;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:8;a:5:{s:4:"time";d:1752580424.551862;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:9;a:5:{s:4:"time";d:1752580424.5519;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"bedezign\yii2\audit\Audit";}i:10;a:5:{s:4:"time";d:1752580424.583514;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"bedezign\yii2\audit\controllers\EntryController";}i:11;a:5:{s:4:"time";d:1752580424.583603;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:12;a:5:{s:4:"time";d:1752580424.585052;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:13;a:5:{s:4:"time";d:1752580424.585124;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:14;a:5:{s:4:"time";d:1752580424.617415;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:15;a:5:{s:4:"time";d:1752580424.622547;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"bedezign\yii2\audit\models\AuditData";}i:16;a:5:{s:4:"time";d:1752580424.630301;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"bedezign\yii2\audit\models\AuditData";}i:17;a:5:{s:4:"time";d:1752580424.630334;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"bedezign\yii2\audit\models\AuditData";}i:18;a:5:{s:4:"time";d:1752580424.630355;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"bedezign\yii2\audit\models\AuditData";}i:19;a:5:{s:4:"time";d:1752580424.630378;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"bedezign\yii2\audit\models\AuditData";}i:20;a:5:{s:4:"time";d:1752580424.632299;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"bedezign\yii2\audit\models\AuditData";}i:21;a:5:{s:4:"time";d:1752580424.632479;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"bedezign\yii2\audit\models\AuditData";}i:22;a:5:{s:4:"time";d:1752580424.632614;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"bedezign\yii2\audit\models\AuditData";}i:23;a:5:{s:4:"time";d:1752580424.646974;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:24;a:5:{s:4:"time";d:1752580424.66297;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:25;a:5:{s:4:"time";d:1752580424.67573;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:26;a:5:{s:4:"time";d:1752580424.688111;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:27;a:5:{s:4:"time";d:1752580424.696372;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:28;a:5:{s:4:"time";d:1752580424.725428;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:29;a:5:{s:4:"time";d:1752580424.725776;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:30;a:5:{s:4:"time";d:1752580424.725823;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:31;a:5:{s:4:"time";d:1752580424.729868;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:32;a:5:{s:4:"time";d:1752580424.746302;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:33;a:5:{s:4:"time";d:1752580424.746354;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:34;a:5:{s:4:"time";d:1752580424.74639;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:35;a:5:{s:4:"time";d:1752580424.746421;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:36;a:5:{s:4:"time";d:1752580424.74645;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:37;a:5:{s:4:"time";d:1752580424.746528;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:38;a:5:{s:4:"time";d:1752580424.74656;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:39;a:5:{s:4:"time";d:1752580424.746623;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:40;a:5:{s:4:"time";d:1752580424.746916;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\DetailView";}i:41;a:5:{s:4:"time";d:1752580424.750944;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\DetailView";}i:42;a:5:{s:4:"time";d:1752580424.751061;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\DetailView";}i:43;a:5:{s:4:"time";d:1752580424.7511;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:44;a:5:{s:4:"time";d:1752580424.751154;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\DetailView";}i:45;a:5:{s:4:"time";d:1752580424.751231;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\DetailView";}i:46;a:5:{s:4:"time";d:1752580424.751449;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\DetailView";}i:47;a:5:{s:4:"time";d:1752580424.752287;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:48;a:5:{s:4:"time";d:1752580424.776081;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:49;a:5:{s:4:"time";d:1752580424.781868;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\grid\GridView";}i:50;a:5:{s:4:"time";d:1752580424.783919;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\grid\GridView";}i:51;a:5:{s:4:"time";d:1752580424.79105;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\grid\GridView";}i:52;a:5:{s:4:"time";d:1752580424.791252;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:53;a:5:{s:4:"time";d:1752580424.791373;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:54;a:5:{s:4:"time";d:1752580424.793786;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:55;a:5:{s:4:"time";d:1752580424.793881;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:56;a:5:{s:4:"time";d:1752580424.794417;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:57;a:5:{s:4:"time";d:1752580424.819184;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:58;a:5:{s:4:"time";d:1752580424.819291;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:59;a:5:{s:4:"time";d:1752580424.819537;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:60;a:5:{s:4:"time";d:1752580424.834007;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap4\NavBar";}i:61;a:5:{s:4:"time";d:1752580424.851104;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:62;a:5:{s:4:"time";d:1752580424.851137;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:63;a:5:{s:4:"time";d:1752580424.851151;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:64;a:5:{s:4:"time";d:1752580424.851165;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:65;a:5:{s:4:"time";d:1752580424.86501;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap4\Nav";}i:66;a:5:{s:4:"time";d:1752580424.865074;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap4\Nav";}i:67;a:5:{s:4:"time";d:1752580424.865299;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap4\Nav";}i:68;a:5:{s:4:"time";d:1752580424.865377;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap4\Nav";}i:69;a:5:{s:4:"time";d:1752580424.865414;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap4\Nav";}i:70;a:5:{s:4:"time";d:1752580424.865462;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap4\Nav";}i:71;a:5:{s:4:"time";d:1752580424.865494;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap4\NavBar";}i:72;a:5:{s:4:"time";d:1752580424.866315;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap4\NavBar";}i:73;a:5:{s:4:"time";d:1752580424.866938;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\widgets\Breadcrumbs";}i:74;a:5:{s:4:"time";d:1752580424.866949;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\widgets\Breadcrumbs";}i:75;a:5:{s:4:"time";d:1752580424.867072;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\widgets\Breadcrumbs";}i:76;a:5:{s:4:"time";d:1752580424.867411;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:77;a:5:{s:4:"time";d:1752580424.874917;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"bedezign\yii2\audit\models\AuditEntry";}i:78;a:5:{s:4:"time";d:1752580424.875039;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:79;a:5:{s:4:"time";d:1752580424.877115;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:80;a:5:{s:4:"time";d:1752580424.877471;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:81;a:5:{s:4:"time";d:1752580424.877975;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:82;a:5:{s:4:"time";d:1752580424.878029;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"bedezign\yii2\audit\controllers\EntryController";}i:83;a:5:{s:4:"time";d:1752580424.878043;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"bedezign\yii2\audit\Audit";}i:84;a:5:{s:4:"time";d:1752580424.878989;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:85;a:5:{s:4:"time";d:1752580424.880236;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:86;a:5:{s:4:"time";d:1752580424.880273;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:87;a:5:{s:4:"time";d:1752580424.880283;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:88;a:5:{s:4:"time";d:1752580424.881162;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:89;a:5:{s:4:"time";d:1752580424.881433;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:1752580424.361948;s:3:"end";d:1752580424.883099;s:6:"memory";i:10219104;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:324:"a:3:{s:8:"messages";a:1:{i:37;a:6:{i:0;s:56:"Pretty URL not enabled. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752580424.539871;i:4;a:0:{}i:5;i:7379144;}}s:5:"route";s:16:"audit/entry/view";s:6:"action";s:61:"bedezign\yii2\audit\controllers\EntryController::actionView()";}";s:7:"request";s:9620:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:17:{s:12:"content-type";s:0:"";s:14:"content-length";s:1:"0";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:4:"none";s:25:"upgrade-insecure-requests";s:1:"1";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:64:""Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:474:"_identity-frontend=1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:10:"Set-Cookie";a:3:{i:0;s:65:"advanced-backend-fmz=3ea04gj3s6igcfrekib7el9nsv; path=/; HttpOnly";i:1;s:311:"_identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D; expires=Thu, 14 Aug 2025 11:53:44 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";i:2;s:221:"_csrf-backend=b972e50216b21932e01aab2a53823476c24c62cc4d4a31c68338b3bb9be08500a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%225uJLdZgaxTJz20TkRQSw7raot9Y2pOjO%22%3B%7D; path=/; HttpOnly; SameSite=Lax";}s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"687641487d54b";s:16:"X-Debug-Duration";s:3:"520";s:12:"X-Debug-Link";s:64:"/backoffice/index.php?r=debug%2Fdefault%2Fview&tag=687641487d54b";}s:5:"route";s:16:"audit/entry/view";s:6:"action";s:61:"bedezign\yii2\audit\controllers\EntryController::actionView()";s:12:"actionParams";a:2:{s:2:"id";s:3:"947";s:5:"panel";s:9:"audit/log";}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:102:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-e00aea21-662e-4e8c-83ed-c9e9fe66e00b";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:462:"C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:21:"/backoffice/index.php";s:3:"URL";s:21:"/backoffice/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:21:"/backoffice/index.php";s:15:"SCRIPT_FILENAME";s:41:"C:\Web\Reclassering\backend\web\index.php";s:11:"REQUEST_URI";s:69:"/backoffice/index.php?r=audit%2Fentry%2Fview&id=947&panel=audit%2Flog";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"58243";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:47:"r=audit%2Fentry%2Fview&id=947&panel=audit%2Flog";s:15:"PATH_TRANSLATED";s:41:"C:\Web\Reclassering\backend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:0:"";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:32:"C:\Web\Reclassering\backend\web\";s:12:"APPL_MD_PATH";s:27:"/LM/W3SVC/2/ROOT/backoffice";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:4:"none";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:64:""Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:474:"_identity-frontend=1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW%22%2C2592000%5D%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:21:"/backoffice/index.php";s:18:"REQUEST_TIME_FLOAT";d:1752580424.344207;s:12:"REQUEST_TIME";i:1752580424;}s:3:"GET";a:3:{s:1:"r";s:16:"audit/entry/view";s:2:"id";s:3:"947";s:5:"panel";s:9:"audit/log";}s:4:"POST";a:0:{}s:6:"COOKIE";a:3:{s:18:"_identity-frontend";s:158:"1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo",2592000]";}";s:16:"sidebar-collapse";s:5:"false";s:17:"_identity-backend";s:157:"39d1ba9859efd2bc37503bb1ffdf6627160ca396ce3e4675a5f71e3c133c960ba:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW",2592000]";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";s:32:"R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW";}}";s:4:"user";s:1549:"a:5:{s:2:"id";i:1;s:8:"identity";a:12:{s:2:"id";s:1:"1";s:8:"username";s:11:"'SuperUser'";s:8:"auth_key";s:34:"'R_0KKN9W6I1Ivlsxc-lrj00bEoRL26MW'";s:13:"password_hash";s:62:"'$2y$13$uUSLwNI3so2bzilzzZdKJ.C1qmfyTdLRRT1XVP8jYO1c38iE6dfxS'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1741788198";s:10:"updated_at";s:10:"1749661338";s:18:"verification_token";s:45:"'oZxacBEp5N7LZAqXc3s1haihOCLK8dLU_1741788198'";s:7:"role_id";s:1:"1";s:12:"access_token";s:66:"'CjYetJZp6PI5vAvKuqm6TDSC_2kYfe_LtOkrjMVtEo4IseTg3J-r-Gp3gMgH-pr7'";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:3750:"a:9:{s:34:"bedezign\yii2\audit\web\AuditAsset";a:9:{s:10:"sourcePath";s:61:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src/web/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\53898977";s:7:"baseUrl";s:27:"/backoffice/assets/53898977";s:7:"depends";a:1:{i:0;s:30:"\yii\bootstrap4\BootstrapAsset";}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:13:"css/audit.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:30:"\yii\bootstrap4\BootstrapAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:22:"yii\grid\GridViewAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:15:"yii.gridView.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\ea09678e";s:7:"baseUrl";s:27:"/backoffice/assets/ea09678e";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:50:"C:\Web\Reclassering/vendor/bower-asset/jquery/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\8bc86ca8";s:7:"baseUrl";s:27:"/backoffice/assets/8bc86ca8";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:21:"yii\widgets\PjaxAsset";a:9:{s:10:"sourcePath";s:48:"C:\Web\Reclassering/vendor/bower-asset/yii2-pjax";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\38002d64";s:7:"baseUrl";s:27:"/backoffice/assets/38002d64";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:14:"jquery.pjax.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:38:"bedezign\yii2\audit\web\JSLoggingAsset";a:9:{s:10:"sourcePath";s:61:"C:\Web\Reclassering\vendor\bedezign\yii2-audit\src/web/assets";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\53898977";s:7:"baseUrl";s:27:"/backoffice/assets/53898977";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:20:"javascript/logger.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:1:{s:8:"position";i:1;}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:1:{s:9:"forceCopy";b:1;}}s:29:"yii\bootstrap4\BootstrapAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap4\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:47:"C:\Web\Reclassering\backend\web\assets\75eeaf84";s:7:"baseUrl";s:27:"/backoffice/assets/75eeaf84";s:7:"depends";a:2:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"687641487d54b";s:3:"url";s:90:"http://localhost:8005/backoffice/index.php?r=audit%2Fentry%2Fview&id=947&panel=audit%2Flog";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:1752580424.344207;s:10:"statusCode";i:200;s:8:"sqlCount";i:17;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10219104;s:14:"processingTime";d:0.5208110809326172;}s:10:"exceptions";a:0:{}}