<?php

namespace common\behaviors;

use yii\base\Behavior;
use yii\db\ActiveRecord;
use common\components\AccessHelper;

class ClearPermissionCacheBehavior extends Behavior
{
    public function events()
    {
        return [
            ActiveRecord::EVENT_AFTER_INSERT => 'clearCache',
            ActiveRecord::EVENT_AFTER_UPDATE => 'clearCache',
            ActiveRecord::EVENT_AFTER_DELETE => 'clearCache',
        ];
    }

    public function clearCache($event)
    {
        $model = $this->owner;

        if (isset($model->user_id)) {
            // Clear user-specific permission cache
            AccessHelper::clearUserCache($model->user_id);
        } elseif (isset($model->role_id)) {
            // Clear cache for all users with this role
            $userIds = (new \yii\db\Query())
                ->select('id')
                ->from('user')
                ->where(['role_id' => $model->role_id])
                ->column();

            foreach ($userIds as $userId) {
                AccessHelper::clearUserCache($userId);
            }
        }
    }
}
