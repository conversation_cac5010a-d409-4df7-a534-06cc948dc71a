<?php

namespace common\components;

use Yii;
use yii\base\Component;
use common\models\RoleFunctionality;
use yii\helpers\ArrayHelper;

class AccessHelper extends Component
{
    private static function getCurrentRoleId()
    {
        $user = Yii::$app->user->identity;
        return $user ? $user->role_id : null;
    }

    // public function canAccess($controller, $action)
    // {
    //     $roleId = self::getCurrentRoleId();

    //     return RoleFunctionality::find()
    //         ->joinWith('functionality f')
    //         ->where(['role_id' => $roleId])
    //         ->andWhere(['f.controller' => $controller, 'f.type' => $action])
    //         ->andWhere(['role_functionality.active' => 1])
    //         ->exists();
    // }

    public function canAccess($route)
    {
        $roleId = self::getCurrentRoleId();

        return RoleFunctionality::find()
            ->where(['role_id' => $roleId])
            ->andWhere(['route' => $route])
            ->andWhere(['active' => 1])
            ->exists();
    }

    // public static function buildActionTemplate()
    // {
    //     $roleId = self::getCurrentRoleId();

    //     $actions = RoleFunctionality::find()
    //         ->alias('rf')
    //         ->joinWith('functionality f') // assumes relation is named `functionality`
    //         ->where([
    //             'rf.role_id' => $roleId,
    //             'rf.active' => 1,
    //         ])
    //         ->select('f.type')
    //         ->asArray()
    //         ->column();

    //     $controller = Yii::$app->controller->id;
    //     $access = Yii::$app->accessHelper;

    //     $template = '';

    //     foreach ($actions as $action) {
    //         if ($access->canAccess($controller, $action)) {
    //             $template .= "{" . $action . "} ";
    //         }
    //     }

    //     return trim($template);
    // }

    public static function buildActionTemplate()
    {
        $roleId = self::getCurrentRoleId();

        $routes = RoleFunctionality::find()
            ->where([
                'role_id' => $roleId,
                'active' => 1,
            ])
            ->select('route')
            ->asArray()
            ->column();

        $controller = Yii::$app->controller->id;
        $access = Yii::$app->accessHelper;

        $template = '';

        foreach ($routes as $route) {
            // Extract action from route
            $action = str_replace('/', '', str_replace($controller . '/', '', $route));
            if ($access->canAccess($route)) {
                $template .= "{" . $action . "} ";
            }
        }

        return trim($template);
    }
}
