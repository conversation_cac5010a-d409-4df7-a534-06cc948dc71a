<?php

namespace common\components;

use Yii;
use yii\base\Component;
use common\models\RoleFunctionality;
use common\models\UserPermission;

class AccessHelper extends Component
{
    private static function getCurrentRoleId()
    {
        $user = Yii::$app->user->identity;
        return $user ? $user->role_id : null;
    }

    public function canAccess($route)
    {
        $user = Yii::$app->user->identity;
        if (!$user) {
            return false;
        }

        $cacheKey = "permissions_user_{$user->id}";
        $permissions = Yii::$app->cache->get($cacheKey);

        if ($permissions === false) {
            $permissions = self::buildPermissions($user->id, $user->role_id);
            Yii::$app->cache->set($cacheKey, $permissions, 3600); // cache for 1 hour
        }

        return isset($permissions[$route]) ? $permissions[$route] : false;
    }

    /**
     * Build merged permissions (role + user override).
     */
    public static function buildPermissions($userId, $roleId)
    {
        $permissions = [];

        // Step 1: Role-based permissions
        $roleRoutes = RoleFunctionality::find()
            ->select(['route'])
            ->where(['role_id' => $roleId, 'active' => 1])
            ->column();

        foreach ($roleRoutes as $route) {
            $permissions[$route] = true;
        }

        // Step 2: User-based overrides
        $userOverrides = UserPermission::find()
            ->select(['route', 'can_access'])
            ->where(['user_id' => $userId])
            ->all();

        foreach ($userOverrides as $override) {
            $permissions[$override->route] = (bool) $override->can_access;
        }

        return $permissions;
    }

    /**
     * Clear a specific user's cached permissions.
     */
    public static function clearUserCache($userId)
    {
        Yii::$app->cache->delete("permissions_user_{$userId}");
    }

    /**
     * Clear all cached permissions.
     */
    public static function clearAllCache()
    {
        Yii::$app->cache->flush();
    }

    public static function buildActionTemplate()
    {
        $roleId = self::getCurrentRoleId();
        if (!$roleId) {
            return '';
        }

        $routes = RoleFunctionality::find()
            ->where([
                'role_id' => $roleId,
                'active' => 1,
            ])
            ->select('route')
            ->asArray()
            ->column();

        $controller = Yii::$app->controller->id;
        $access = Yii::$app->accessHelper;

        $template = '';

        foreach ($routes as $route) {
            // Extract action from route
            $action = str_replace('/', '', str_replace($controller . '/', '', $route));
            if ($access->canAccess($route)) {
                $template .= "{" . $action . "} ";
            }
        }

        return trim($template);
    }
}
