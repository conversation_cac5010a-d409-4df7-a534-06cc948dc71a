<?php

namespace console\controllers;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;
use common\models\RoleFunctionality;
use common\models\Role;
use common\models\User;
use common\components\AccessHelper;

/**
 * Console controller for managing access permissions
 */
class AccessController extends Controller
{
    /**
     * Lists all role functionalities
     */
    public function actionList()
    {
        $roleFunctionalities = RoleFunctionality::find()
            ->with(['role'])
            ->orderBy(['role_id' => SORT_ASC, 'route' => SORT_ASC])
            ->all();

        if (empty($roleFunctionalities)) {
            $this->stdout("No role functionalities configured.\n");
            return ExitCode::OK;
        }

        $this->stdout("Role Functionalities:\n");
        $this->stdout(str_repeat("-", 80) . "\n");

        $currentRoleId = null;
        foreach ($roleFunctionalities as $rf) {
            if ($currentRoleId !== $rf->role_id) {
                $currentRoleId = $rf->role_id;
                $roleName = $rf->role ? $rf->role->name : 'Unknown Role';
                $this->stdout("\nRole: {$roleName} (ID: {$rf->role_id})\n");
                $this->stdout(str_repeat("-", 40) . "\n");
            }

            $status = $rf->active ? 'Active' : 'Inactive';
            $this->stdout("  Route: {$rf->route} | Status: {$status}\n");
        }

        return ExitCode::OK;
    }

    /**
     * Tests access for a specific user and route
     * @param int $userId User ID
     * @param string $route Route to test
     */
    public function actionTest($userId, $route)
    {
        $user = User::findOne($userId);
        if (!$user) {
            $this->stderr("User with ID {$userId} not found.\n");
            return ExitCode::DATAERR;
        }

        $this->stdout("Testing access for user: {$user->username} (ID: {$userId})\n");
        $this->stdout("Role: {$user->role->name} (ID: {$user->role_id})\n");
        $this->stdout("Route: {$route}\n");
        $this->stdout(str_repeat("-", 50) . "\n");

        // Get permissions for this user
        $permissions = AccessHelper::buildPermissions($user->id, $user->role_id);

        $this->stdout("All permissions for this user:\n");
        if (empty($permissions)) {
            $this->stdout("  No permissions found!\n");
        } else {
            foreach ($permissions as $permRoute => $allowed) {
                $status = $allowed ? 'ALLOWED' : 'DENIED';
                $this->stdout("  {$permRoute}: {$status}\n");
            }
        }

        $this->stdout("\nSpecific route test:\n");
        $hasAccess = isset($permissions[$route]) ? $permissions[$route] : false;
        $result = $hasAccess ? 'ALLOWED' : 'DENIED';
        $this->stdout("  {$route}: {$result}\n");

        return ExitCode::OK;
    }

    /**
     * Adds a route permission for a role
     * @param int $roleId Role ID
     * @param string $route Route to add
     */
    public function actionAdd($roleId, $route)
    {
        $role = Role::findOne($roleId);
        if (!$role) {
            $this->stderr("Role with ID {$roleId} not found.\n");
            return ExitCode::DATAERR;
        }

        $existing = RoleFunctionality::findOne(['role_id' => $roleId, 'route' => $route]);
        if ($existing) {
            $this->stdout("Route '{$route}' already exists for role '{$role->name}'.\n");
            if (!$existing->active) {
                $existing->active = 1;
                if ($existing->save()) {
                    $this->stdout("Route activated.\n");
                    AccessHelper::clearAllCache();
                    return ExitCode::OK;
                }
            }
            return ExitCode::OK;
        }

        $roleFunctionality = new RoleFunctionality([
            'role_id' => $roleId,
            'route' => $route,
            'active' => 1,
        ]);

        if ($roleFunctionality->save()) {
            $this->stdout("Route '{$route}' added for role '{$role->name}'.\n");
            AccessHelper::clearAllCache();
            return ExitCode::OK;
        } else {
            $this->stderr("Failed to add route: " . json_encode($roleFunctionality->errors) . "\n");
            return ExitCode::DATAERR;
        }
    }

    /**
     * Removes a route permission for a role
     * @param int $roleId Role ID
     * @param string $route Route to remove
     */
    public function actionRemove($roleId, $route)
    {
        $role = Role::findOne($roleId);
        if (!$role) {
            $this->stderr("Role with ID {$roleId} not found.\n");
            return ExitCode::DATAERR;
        }

        $roleFunctionality = RoleFunctionality::findOne(['role_id' => $roleId, 'route' => $route]);
        if (!$roleFunctionality) {
            $this->stderr("Route '{$route}' not found for role '{$role->name}'.\n");
            return ExitCode::DATAERR;
        }

        if ($roleFunctionality->delete()) {
            $this->stdout("Route '{$route}' removed from role '{$role->name}'.\n");
            AccessHelper::clearAllCache();
            return ExitCode::OK;
        } else {
            $this->stderr("Failed to remove route.\n");
            return ExitCode::DATAERR;
        }
    }

    /**
     * Clears all cached permissions
     */
    public function actionClearCache()
    {
        AccessHelper::clearAllCache();
        $this->stdout("All cached permissions cleared.\n");
        return ExitCode::OK;
    }

    /**
     * Lists all available roles
     */
    public function actionListRoles()
    {
        $roles = Role::find()->all();

        if (empty($roles)) {
            $this->stdout("No roles found.\n");
            return ExitCode::OK;
        }

        $this->stdout("Available Roles:\n");
        $this->stdout(str_repeat("-", 40) . "\n");

        foreach ($roles as $role) {
            $this->stdout("ID: {$role->id} | Name: {$role->name}\n");
            if ($role->description) {
                $this->stdout("Description: {$role->description}\n");
            }
            $this->stdout(str_repeat("-", 40) . "\n");
        }

        return ExitCode::OK;
    }

    /**
     * Lists users by role
     * @param int $roleId Role ID (optional)
     */
    public function actionListUsers($roleId = null)
    {
        $query = User::find()->with('role');

        if ($roleId) {
            $query->where(['role_id' => $roleId]);
        }

        $users = $query->all();

        if (empty($users)) {
            $this->stdout("No users found.\n");
            return ExitCode::OK;
        }

        $this->stdout("Users:\n");
        $this->stdout(str_repeat("-", 60) . "\n");

        foreach ($users as $user) {
            $roleName = $user->role ? $user->role->name : 'No Role';
            $this->stdout("ID: {$user->id} | Username: {$user->username} | Role: {$roleName} (ID: {$user->role_id})\n");
            $this->stdout("Email: {$user->email} | Status: {$user->status}\n");
            $this->stdout(str_repeat("-", 60) . "\n");
        }

        return ExitCode::OK;
    }
}
