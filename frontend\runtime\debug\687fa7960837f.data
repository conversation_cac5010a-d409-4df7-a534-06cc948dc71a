a:14:{s:6:"config";s:15217:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.53";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.53";s:4:"name";s:3:"FMZ";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:68:{s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\Reclassering\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\Reclassering\vendor/yiisoft/yii2-symfonymailer/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte-widgets/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap4/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\Reclassering\vendor/hail812/yii2-adminlte3/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-mpdf/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-dialog/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\Reclassering\vendor/kartik-v/yii2-popover-x/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-builder/src";}}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-editable/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\Reclassering\vendor/kartik-v/yii2-grid/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-helpers/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-context-menu/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-datecontrol/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-httpclient/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\Reclassering\vendor/kartik-v/yii2-detail-view/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-date-range/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-dynagrid/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\Reclassering\vendor/yiisoft/yii2-jui";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-label-inplace/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-icons/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-money/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-markdown/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\Reclassering\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\Reclassering\vendor/kartik-v/yii2-slider";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-tabs-x/src";}}s:30:"kartik-v/yii2-widget-typeahead";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-typeahead";s:7:"version";s:7:"1.0.4.0";s:5:"alias";a:1:{s:17:"@kartik/typeahead";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-typeahead/src";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-social/src";}}s:30:"kartik-v/yii2-widget-touchspin";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-touchspin";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:17:"@kartik/touchspin";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-touchspin/src";}}s:31:"kartik-v/yii2-widget-timepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-timepicker";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/time";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-timepicker/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-switchinput";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:28:"kartik-v/yii2-widget-spinner";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-spinner";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/spinner";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-spinner/src";}}s:28:"kartik-v/yii2-widget-sidenav";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-sidenav";s:7:"version";s:7:"1.0.1.0";s:5:"alias";a:1:{s:15:"@kartik/sidenav";s:55:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-sidenav";}}s:27:"kartik-v/yii2-widget-rating";a:3:{s:4:"name";s:27:"kartik-v/yii2-widget-rating";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:14:"@kartik/rating";s:58:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rating/src";}}s:31:"kartik-v/yii2-widget-rangeinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-rangeinput";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:13:"@kartik/range";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-rangeinput/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-growl/src";}}s:28:"kartik-v/yii2-widget-depdrop";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-depdrop";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:15:"@kartik/depdrop";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-depdrop/src";}}s:31:"kartik-v/yii2-widget-colorinput";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-colorinput";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:13:"@kartik/color";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-colorinput/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:7:"1.5.1.0";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:66:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:31:"kartik-v/yii2-widget-datepicker";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-datepicker";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:12:"@kartik/date";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-datepicker/src";}}s:26:"kartik-v/yii2-widget-alert";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-alert";s:7:"version";s:7:"1.1.5.0";s:5:"alias";a:1:{s:13:"@kartik/alert";s:57:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-alert/src";}}s:26:"kartik-v/yii2-widget-affix";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-affix";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:13:"@kartik/affix";s:53:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-affix";}}s:21:"kartik-v/yii2-widgets";a:3:{s:4:"name";s:21:"kartik-v/yii2-widgets";s:7:"version";s:7:"3.4.1.0";s:5:"alias";a:1:{s:15:"@kartik/widgets";s:52:"C:\Web\Reclassering\vendor/kartik-v/yii2-widgets/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\Reclassering\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\Reclassering\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-select2/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-activeform/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow/src";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\Reclassering\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\Reclassering\vendor/kartik-v/yii2-export/src";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\Reclassering\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\Reclassering\vendor/cornernote/yii2-workflow-manager/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\Reclassering\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\Reclassering\vendor/yiisoft/yii2-twig/src";}}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\Reclassering\vendor/yiisoft/yii2-debug/src";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\Reclassering\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\Reclassering\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:30:"kartik-v/yii2-widget-fileinput";a:3:{s:4:"name";s:30:"kartik-v/yii2-widget-fileinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/file";s:61:"C:\Web\Reclassering\vendor/kartik-v/yii2-widget-fileinput/src";}}}}";s:3:"log";s:32422:"a:1:{s:8:"messages";a:71:{i:0;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753196437.914571;i:4;a:0:{}i:5;i:2892688;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753196437.917559;i:4;a:0:{}i:5;i:2996632;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753196437.917577;i:4;a:0:{}i:5;i:2996928;}i:3;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753196437.918642;i:4;a:0:{}i:5;i:3026928;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753196437.919503;i:4;a:0:{}i:5;i:3054400;}i:5;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753196437.923399;i:4;a:0:{}i:5;i:3207976;}i:6;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1753196437.923434;i:4;a:0:{}i:5;i:3208616;}i:7;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1753196437.954523;i:4;a:0:{}i:5;i:4206368;}i:8;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196437.988016;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5530464;}i:9;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1753196437.988109;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5532744;}i:14;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196437.995909;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5590560;}i:17;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196437.998414;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5611280;}i:20;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=7) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.008896;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6062048;}i:23;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753196438.032146;i:4;a:0:{}i:5;i:6773168;}i:24;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1753196438.039657;i:4;a:0:{}i:5;i:7026616;}i:31;a:6:{i:0;s:36:"Route requested: 'document/dossiers'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1753196438.042342;i:4;a:0:{}i:5;i:7065768;}i:32;a:6:{i:0;s:31:"Route to run: document/dossiers";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1753196438.04967;i:4;a:0:{}i:5;i:7222784;}i:33;a:6:{i:0;s:73:"Running action: frontend\controllers\DocumentController::actionDossiers()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1753196438.058027;i:4;a:0:{}i:5;i:7343112;}i:34;a:6:{i:0;s:24:"SELECT * FROM `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.059274;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7386344;}i:37;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.071689;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7694864;}i:40;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.074465;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7708776;}i:43;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.075513;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7709208;}i:46;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.082367;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8092256;}i:49;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.084674;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8101600;}i:52;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.085418;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8105920;}i:55;a:6:{i:0;s:87:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='finale')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.086934;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8109776;}i:58;a:6:{i:0;s:96:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='finale')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.089087;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8508984;}i:61;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.092737;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8534864;}i:64;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.093886;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8543624;}i:67;a:6:{i:0;s:31:"SELECT * FROM `document_upload`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.09764;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8572848;}i:70;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `document_upload`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.099593;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8582656;}i:73;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.103074;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8594296;}i:76;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_upload' AND `kcu`.`TABLE_NAME` = 'document_upload'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.104717;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8594112;}i:79;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.10928;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8626008;}i:82;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.110139;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8638272;}i:85;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.113172;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8647976;}i:88;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.115049;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8648624;}i:91;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=7";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.118768;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8689560;}i:94;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.119721;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8701856;}i:97;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.120195;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8715976;}i:100;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.120643;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8728936;}i:103;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.121145;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8743056;}i:106;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.121653;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8756016;}i:109;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.122138;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8770120;}i:112;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.122757;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8783104;}i:115;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.123838;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8795936;}i:118;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.124619;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8809272;}i:121;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.125215;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8822320;}i:124;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.125743;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8835152;}i:127;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.1262;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8848168;}i:130;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.126611;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8866856;}i:133;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.127257;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8880968;}i:136;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=4";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.128184;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8894056;}i:139;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.129493;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8911792;}i:142;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.130198;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8924144;}i:145;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.130863;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8937000;}i:148;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.13139;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8949968;}i:151;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.131837;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8963672;}i:154;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.132354;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8977792;}i:157;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.132889;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8990656;}i:160;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.133234;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9002784;}i:163;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.133593;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9014936;}i:166;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.134161;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9027072;}i:169;a:6:{i:0;s:77:"Rendering view file: C:\Web\Reclassering\frontend\views\document\dossiers.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1753196438.13667;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9165560;}i:170;a:6:{i:0;s:72:"Rendering view file: C:\Web\Reclassering\frontend\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1753196438.149158;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9590192;}i:171;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=7) AND (`is_read`=0)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.172432;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\Reclassering\frontend\views\layouts\main.php";s:4:"line";i:107;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9824288;}i:174;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=7 ORDER BY `created_at` DESC LIMIT 10";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.173474;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\Reclassering\frontend\views\layouts\main.php";s:4:"line";i:107;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9829224;}i:177;a:6:{i:0;s:79:"Rendering view file: C:\Web\Reclassering\common\widgets\views\notifications.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1753196438.174332;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:27;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\Reclassering\frontend\views\layouts\main.php";s:4:"line";i:107;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9831392;}i:178;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\common\widgets\views\profile.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1753196438.17666;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\ProfileWidget.php";s:4:"line";i:32;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\Reclassering\frontend\views\layouts\main.php";s:4:"line";i:110;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9843248;}i:179;a:6:{i:0;s:73:"Rendering view file: C:\Web\Reclassering\common\widgets\views\sidebar.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1753196438.178146;i:4;a:3:{i:0;a:5:{s:4:"file";s:52:"C:\Web\Reclassering\common\widgets\SidebarWidget.php";s:4:"line";i:11;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\Reclassering\frontend\views\layouts\main.php";s:4:"line";i:116;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9854400;}i:180;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/dossiers'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.203227;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:10192072;}}}";s:9:"profiling";s:54702:"a:3:{s:6:"memory";i:10409472;s:4:"time";d:0.30496883392333984;s:8:"messages";a:106:{i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1753196437.988138;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5533552;}i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=reclassering";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1753196437.992125;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5576856;}i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196437.992151;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5576640;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196437.995781;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5589272;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196437.99594;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5591472;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196437.997041;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5594048;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196437.998445;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5612320;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.000569;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5614848;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=7) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.008961;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6062432;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=7) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.009917;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6064632;}i:35;a:6:{i:0;s:24:"SELECT * FROM `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.0593;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7387256;}i:36;a:6:{i:0;s:24:"SELECT * FROM `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.060726;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7414152;}i:38;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.071753;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7696152;}i:39;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.074412;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7707112;}i:41;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.074479;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7710064;}i:42;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.075147;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7712992;}i:44;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.075564;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7710624;}i:45;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.077552;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7714608;}i:47;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.082431;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8093920;}i:48;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.084628;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8099560;}i:50;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.084686;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8103264;}i:51;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.085333;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8105672;}i:53;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.08543;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8107712;}i:54;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.086722;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8110320;}i:56;a:6:{i:0;s:87:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='finale')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.086961;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8111552;}i:57;a:6:{i:0;s:87:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='finale')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.087293;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8113824;}i:59;a:6:{i:0;s:96:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='finale')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.089106;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8510760;}i:60;a:6:{i:0;s:96:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='finale')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.089846;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8512632;}i:62;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.092785;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8536640;}i:63;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.093552;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8538928;}i:65;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.093904;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8546680;}i:66;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.094562;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8548552;}i:68;a:6:{i:0;s:31:"SELECT * FROM `document_upload`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.097714;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8573760;}i:69;a:6:{i:0;s:31:"SELECT * FROM `document_upload`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.099479;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8577904;}i:71;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `document_upload`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.099615;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8583568;}i:72;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `document_upload`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.102977;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8593000;}i:74;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.103095;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8595208;}i:75;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.104531;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8597248;}i:77;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_upload' AND `kcu`.`TABLE_NAME` = 'document_upload'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.104738;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8595152;}i:78;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_upload' AND `kcu`.`TABLE_NAME` = 'document_upload'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.108549;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8597696;}i:80;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.109302;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8627136;}i:81;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.109969;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8629496;}i:83;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.110168;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8639184;}i:84;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.113034;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8646680;}i:86;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.113218;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8648888;}i:87;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.114704;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8650928;}i:89;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.115076;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8649664;}i:90;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.117967;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8652752;}i:92;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.118784;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8690688;}i:93;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.119361;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8692272;}i:95;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.119744;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8702984;}i:96;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.120046;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8706376;}i:98;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.120206;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8717104;}i:99;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.120532;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8719352;}i:101;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.120652;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8730064;}i:102;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.121012;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8733456;}i:104;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.121155;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8744184;}i:105;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.121524;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8746432;}i:107;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.121663;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8757144;}i:108;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.12201;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8760536;}i:110;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.12215;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8771248;}i:111;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.122524;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8773496;}i:113;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.12277;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8784232;}i:114;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.123376;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8786592;}i:116;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.123881;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8797064;}i:117;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.124451;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8799424;}i:119;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.124631;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8810400;}i:120;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.12508;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8812760;}i:122;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.125227;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8823448;}i:123;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.125634;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8825808;}i:125;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.125751;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8836280;}i:126;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.126085;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8838640;}i:128;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.126209;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8849296;}i:129;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.12652;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8857288;}i:131;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.126618;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8867984;}i:132;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.126934;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8871376;}i:134;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.127291;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8882096;}i:135;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.127878;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8884456;}i:137;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.128202;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8895184;}i:138;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.129252;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8896784;}i:140;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.129508;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8912920;}i:141;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.129981;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8914552;}i:143;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.130213;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8925272;}i:144;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.130602;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8927632;}i:146;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.130891;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8938128;}i:147;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.131258;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8940376;}i:149;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.131399;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8951096;}i:150;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.131737;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8953456;}i:152;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.131844;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8964800;}i:153;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.132213;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8968192;}i:155;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.132365;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8978920;}i:156;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.13276;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8981280;}i:158;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.132899;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8991784;}i:159;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.133145;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8993416;}i:161;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.133241;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9003912;}i:162;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.13351;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9005544;}i:164;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.1336;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9016064;}i:165;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.13408;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9017696;}i:167;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.134172;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9028200;}i:168;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.134416;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9029832;}i:172;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=7) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.172515;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\Reclassering\frontend\views\layouts\main.php";s:4:"line";i:107;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9826064;}i:173;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=7) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.173384;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\Reclassering\frontend\views\layouts\main.php";s:4:"line";i:107;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9827800;}i:175;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=7 ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.173487;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\Reclassering\frontend\views\layouts\main.php";s:4:"line";i:107;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9831104;}i:176;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=7 ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.174063;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\Reclassering\frontend\views\layouts\main.php";s:4:"line";i:107;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9833080;}i:181;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/dossiers'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.203267;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:10193200;}i:182;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/dossiers'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.204104;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:10194352;}}}";s:2:"db";s:53931:"a:1:{s:8:"messages";a:104:{i:12;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196437.992151;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5576640;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196437.995781;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5589272;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196437.99594;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5591472;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196437.997041;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5594048;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196437.998445;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5612320;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.000569;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5614848;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=7) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.008961;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6062432;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=7) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.009917;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\Reclassering\common\models\User.php";s:4:"line";i:78;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6064632;}i:35;a:6:{i:0;s:24:"SELECT * FROM `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.0593;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7387256;}i:36;a:6:{i:0;s:24:"SELECT * FROM `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.060726;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7414152;}i:38;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.071753;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7696152;}i:39;a:6:{i:0;s:33:"SHOW FULL COLUMNS FROM `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.074412;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7707112;}i:41;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.074479;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7710064;}i:42;a:6:{i:0;s:28:"SHOW CREATE TABLE `document`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.075147;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7712992;}i:44;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.075564;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7710624;}i:45;a:6:{i:0;s:772:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document' AND `kcu`.`TABLE_NAME` = 'document'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.077552;i:4;a:2:{i:0;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:257;s:8:"function";s:12:"hasAttribute";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:7714608;}i:47;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.082431;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8093920;}i:48;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.084628;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8099560;}i:50;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.084686;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8103264;}i:51;a:6:{i:0;s:29:"SHOW CREATE TABLE `sw_status`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.085333;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8105672;}i:53;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.08543;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8107712;}i:54;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'sw_status' AND `kcu`.`TABLE_NAME` = 'sw_status'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.086722;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8110320;}i:56;a:6:{i:0;s:87:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='finale')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.086961;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8111552;}i:57;a:6:{i:0;s:87:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='finale')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.087293;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8113824;}i:59;a:6:{i:0;s:96:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='finale')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.089106;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8510760;}i:60;a:6:{i:0;s:96:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='finale')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.089846;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8512632;}i:62;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.092785;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8536640;}i:63;a:6:{i:0;s:91:"SELECT * FROM `sw_status` WHERE (`workflow_id`='document-workflow') AND (`id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.093552;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:166;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8538928;}i:65;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.093904;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8546680;}i:66;a:6:{i:0;s:100:"SELECT * FROM `sw_metadata` WHERE (`workflow_id`='document-workflow') AND (`status_id`='medewerker')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.094562;i:4;a:3:{i:0;a:5:{s:4:"file";s:95:"C:\Web\Reclassering\vendor\cornernote\yii2-workflow-manager\src\components\WorkflowDbSource.php";s:4:"line";i:179;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"C:\Web\Reclassering\vendor\raoul2000\yii2-workflow\src\base\SimpleWorkflowBehavior.php";s:4:"line";i:334;s:8:"function";s:9:"getStatus";s:5:"class";s:55:"cornernote\workflow\manager\components\WorkflowDbSource";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:545;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8548552;}i:68;a:6:{i:0;s:31:"SELECT * FROM `document_upload`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.097714;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8573760;}i:69;a:6:{i:0;s:31:"SELECT * FROM `document_upload`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.099479;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8577904;}i:71;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `document_upload`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.099615;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8583568;}i:72;a:6:{i:0;s:40:"SHOW FULL COLUMNS FROM `document_upload`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.102977;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8593000;}i:74;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.103095;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8595208;}i:75;a:6:{i:0;s:35:"SHOW CREATE TABLE `document_upload`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.104531;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8597248;}i:77;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_upload' AND `kcu`.`TABLE_NAME` = 'document_upload'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.104738;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8595152;}i:78;a:6:{i:0;s:786:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_upload' AND `kcu`.`TABLE_NAME` = 'document_upload'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.108549;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:546;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8597696;}i:80;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.109302;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8627136;}i:81;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.109969;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8629496;}i:83;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.110168;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8639184;}i:84;a:6:{i:0;s:38:"SHOW FULL COLUMNS FROM `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.113034;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8646680;}i:86;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.113218;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8648888;}i:87;a:6:{i:0;s:33:"SHOW CREATE TABLE `document_type`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.114704;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8650928;}i:89;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.115076;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8649664;}i:90;a:6:{i:0;s:782:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'document_type' AND `kcu`.`TABLE_NAME` = 'document_type'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.117967;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8652752;}i:92;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=7";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.118784;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8690688;}i:93;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=7";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.119361;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8692272;}i:95;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.119744;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8702984;}i:96;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.120046;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8706376;}i:98;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.120206;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8717104;}i:99;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.120532;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8719352;}i:101;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.120652;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8730064;}i:102;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.121012;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8733456;}i:104;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.121155;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8744184;}i:105;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.121524;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8746432;}i:107;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.121663;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8757144;}i:108;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.12201;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8760536;}i:110;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.12215;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8771248;}i:111;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.122524;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8773496;}i:113;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.12277;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8784232;}i:114;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.123376;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8786592;}i:116;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.123881;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8797064;}i:117;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.124451;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8799424;}i:119;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.124631;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8810400;}i:120;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.12508;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8812760;}i:122;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.125227;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8823448;}i:123;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.125634;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8825808;}i:125;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.125751;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8836280;}i:126;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.126085;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8838640;}i:128;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.126209;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8849296;}i:129;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.12652;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8857288;}i:131;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.126618;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8867984;}i:132;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.126934;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8871376;}i:134;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.127291;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8882096;}i:135;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.127878;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8884456;}i:137;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=4";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.128202;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8895184;}i:138;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=4";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.129252;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8896784;}i:140;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.129508;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8912920;}i:141;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.129981;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8914552;}i:143;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.130213;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8925272;}i:144;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.130602;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8927632;}i:146;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.130891;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8938128;}i:147;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=9";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.131258;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8940376;}i:149;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.131399;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8951096;}i:150;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.131737;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8953456;}i:152;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.131844;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8964800;}i:153;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=8";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.132213;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8968192;}i:155;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.132365;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8978920;}i:156;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=6";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.13276;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8981280;}i:158;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.132899;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8991784;}i:159;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.133145;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:8993416;}i:161;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.133241;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9003912;}i:162;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.13351;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9005544;}i:164;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.1336;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9016064;}i:165;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.13408;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9017696;}i:167;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.134172;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9028200;}i:168;a:6:{i:0;s:42:"SELECT * FROM `document_type` WHERE `id`=5";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.134416;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:551;s:8:"function";s:5:"__get";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}}i:5;i:9029832;}i:172;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=7) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.172515;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\Reclassering\frontend\views\layouts\main.php";s:4:"line";i:107;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9826064;}i:173;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `notification_user` WHERE (`user_id`=7) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.173384;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\Reclassering\frontend\views\layouts\main.php";s:4:"line";i:107;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9827800;}i:175;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=7 ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.173487;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\Reclassering\frontend\views\layouts\main.php";s:4:"line";i:107;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9831104;}i:176;a:6:{i:0;s:87:"SELECT * FROM `notification_user` WHERE `user_id`=7 ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.174063;i:4;a:3:{i:0;a:5:{s:4:"file";s:57:"C:\Web\Reclassering\common\widgets\NotificationWidget.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\Reclassering\frontend\views\layouts\main.php";s:4:"line";i:107;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:63:"C:\Web\Reclassering\frontend\controllers\DocumentController.php";s:4:"line";i:572;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9833080;}i:181;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/dossiers'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.203267;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:10193200;}i:182;a:6:{i:0;s:70:"SELECT * FROM `notification_trigger` WHERE `route`='document/dossiers'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1753196438.204104;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\Reclassering\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:10194352;}}}";s:5:"event";s:34908:"a:200:{i:0;a:5:{s:4:"time";d:1753196437.978699;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1753196437.992112;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1753196438.010534;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:1753196438.010578;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:1753196438.02464;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:1753196438.040934;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1753196438.056339;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:7;a:5:{s:4:"time";d:1753196438.057963;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"frontend\controllers\DocumentController";}i:8;a:5:{s:4:"time";d:1753196438.059151;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:9;a:5:{s:4:"time";d:1753196438.077668;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:10;a:5:{s:4:"time";d:1753196438.078934;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:11;a:5:{s:4:"time";d:1753196438.078982;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:12;a:5:{s:4:"time";d:1753196438.079015;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:13;a:5:{s:4:"time";d:1753196438.079044;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:14;a:5:{s:4:"time";d:1753196438.079073;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:15;a:5:{s:4:"time";d:1753196438.079101;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:16;a:5:{s:4:"time";d:1753196438.079129;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:17;a:5:{s:4:"time";d:1753196438.079156;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:18;a:5:{s:4:"time";d:1753196438.079184;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:19;a:5:{s:4:"time";d:1753196438.079211;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:20;a:5:{s:4:"time";d:1753196438.079245;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:21;a:5:{s:4:"time";d:1753196438.079373;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:22;a:5:{s:4:"time";d:1753196438.079492;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:23;a:5:{s:4:"time";d:1753196438.079579;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:24;a:5:{s:4:"time";d:1753196438.079658;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:25;a:5:{s:4:"time";d:1753196438.079735;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:26;a:5:{s:4:"time";d:1753196438.079812;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:27;a:5:{s:4:"time";d:1753196438.079889;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:28;a:5:{s:4:"time";d:1753196438.079959;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:29;a:5:{s:4:"time";d:1753196438.080029;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:30;a:5:{s:4:"time";d:1753196438.080157;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:31;a:5:{s:4:"time";d:1753196438.080251;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:32;a:5:{s:4:"time";d:1753196438.080333;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:33;a:5:{s:4:"time";d:1753196438.080414;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:34;a:5:{s:4:"time";d:1753196438.080489;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:35;a:5:{s:4:"time";d:1753196438.080569;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:36;a:5:{s:4:"time";d:1753196438.08227;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:37;a:5:{s:4:"time";d:1753196438.087313;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:38;a:5:{s:4:"time";d:1753196438.087333;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:39;a:5:{s:4:"time";d:1753196438.087667;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:40;a:5:{s:4:"time";d:1753196438.09225;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:41;a:5:{s:4:"time";d:1753196438.092396;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:42;a:5:{s:4:"time";d:1753196438.093601;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:43;a:5:{s:4:"time";d:1753196438.0937;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"cornernote\workflow\manager\models\Status";}i:44;a:5:{s:4:"time";d:1753196438.093723;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:45;a:5:{s:4:"time";d:1753196438.094642;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:46;a:5:{s:4:"time";d:1753196438.094683;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:47;a:5:{s:4:"time";d:1753196438.094697;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:48;a:5:{s:4:"time";d:1753196438.094707;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:49;a:5:{s:4:"time";d:1753196438.094717;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:50;a:5:{s:4:"time";d:1753196438.094728;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:51;a:5:{s:4:"time";d:1753196438.094737;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:52;a:5:{s:4:"time";d:1753196438.094748;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:53;a:5:{s:4:"time";d:1753196438.094758;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:54;a:5:{s:4:"time";d:1753196438.094768;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:55;a:5:{s:4:"time";d:1753196438.094778;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:56;a:5:{s:4:"time";d:1753196438.094787;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:57;a:5:{s:4:"time";d:1753196438.094797;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:58;a:5:{s:4:"time";d:1753196438.094806;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:59;a:5:{s:4:"time";d:1753196438.094816;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:60;a:5:{s:4:"time";d:1753196438.094825;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:61;a:5:{s:4:"time";d:1753196438.094836;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:62;a:5:{s:4:"time";d:1753196438.094846;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:63;a:5:{s:4:"time";d:1753196438.094855;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:64;a:5:{s:4:"time";d:1753196438.09499;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:65;a:5:{s:4:"time";d:1753196438.095054;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:66;a:5:{s:4:"time";d:1753196438.09507;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:67;a:5:{s:4:"time";d:1753196438.095084;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:68;a:5:{s:4:"time";d:1753196438.095097;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:69;a:5:{s:4:"time";d:1753196438.095114;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:70;a:5:{s:4:"time";d:1753196438.095132;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\Document";}i:71;a:5:{s:4:"time";d:1753196438.097505;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:72;a:5:{s:4:"time";d:1753196438.099528;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\models\DocumentUpload";}i:73;a:5:{s:4:"time";d:1753196438.108636;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\models\DocumentUpload";}i:74;a:5:{s:4:"time";d:1753196438.108656;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\models\DocumentUpload";}i:75;a:5:{s:4:"time";d:1753196438.108669;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\models\DocumentUpload";}i:76;a:5:{s:4:"time";d:1753196438.108672;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\models\DocumentUpload";}i:77;a:5:{s:4:"time";d:1753196438.108676;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\models\DocumentUpload";}i:78;a:5:{s:4:"time";d:1753196438.109179;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:79;a:5:{s:4:"time";d:1753196438.110086;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:80;a:5:{s:4:"time";d:1753196438.118042;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:81;a:5:{s:4:"time";d:1753196438.118699;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:82;a:5:{s:4:"time";d:1753196438.119447;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:83;a:5:{s:4:"time";d:1753196438.119494;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:84;a:5:{s:4:"time";d:1753196438.119608;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:85;a:5:{s:4:"time";d:1753196438.120088;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:86;a:5:{s:4:"time";d:1753196438.120107;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:87;a:5:{s:4:"time";d:1753196438.120148;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:88;a:5:{s:4:"time";d:1753196438.120558;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:89;a:5:{s:4:"time";d:1753196438.120573;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:90;a:5:{s:4:"time";d:1753196438.120606;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:91;a:5:{s:4:"time";d:1753196438.121044;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:92;a:5:{s:4:"time";d:1753196438.121062;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:93;a:5:{s:4:"time";d:1753196438.121099;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:94;a:5:{s:4:"time";d:1753196438.121558;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:95;a:5:{s:4:"time";d:1753196438.121575;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:96;a:5:{s:4:"time";d:1753196438.121611;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:97;a:5:{s:4:"time";d:1753196438.122041;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:98;a:5:{s:4:"time";d:1753196438.122057;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:99;a:5:{s:4:"time";d:1753196438.122093;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:100;a:5:{s:4:"time";d:1753196438.122634;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:101;a:5:{s:4:"time";d:1753196438.122656;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:102;a:5:{s:4:"time";d:1753196438.122703;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:103;a:5:{s:4:"time";d:1753196438.123524;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:104;a:5:{s:4:"time";d:1753196438.123585;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:105;a:5:{s:4:"time";d:1753196438.123682;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:106;a:5:{s:4:"time";d:1753196438.1245;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:107;a:5:{s:4:"time";d:1753196438.124523;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:108;a:5:{s:4:"time";d:1753196438.124568;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:109;a:5:{s:4:"time";d:1753196438.125113;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:110;a:5:{s:4:"time";d:1753196438.125131;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:111;a:5:{s:4:"time";d:1753196438.125169;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:112;a:5:{s:4:"time";d:1753196438.125666;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:113;a:5:{s:4:"time";d:1753196438.12568;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:114;a:5:{s:4:"time";d:1753196438.125709;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:115;a:5:{s:4:"time";d:1753196438.126116;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:116;a:5:{s:4:"time";d:1753196438.126131;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:117;a:5:{s:4:"time";d:1753196438.126162;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:118;a:5:{s:4:"time";d:1753196438.126547;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:119;a:5:{s:4:"time";d:1753196438.126563;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:120;a:5:{s:4:"time";d:1753196438.126584;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:121;a:5:{s:4:"time";d:1753196438.127019;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:122;a:5:{s:4:"time";d:1753196438.127073;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:123;a:5:{s:4:"time";d:1753196438.127176;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:124;a:5:{s:4:"time";d:1753196438.127979;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:125;a:5:{s:4:"time";d:1753196438.128015;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:126;a:5:{s:4:"time";d:1753196438.128096;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:127;a:5:{s:4:"time";d:1753196438.129319;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:128;a:5:{s:4:"time";d:1753196438.12935;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:129;a:5:{s:4:"time";d:1753196438.129415;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:130;a:5:{s:4:"time";d:1753196438.130048;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:131;a:5:{s:4:"time";d:1753196438.130075;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:132;a:5:{s:4:"time";d:1753196438.130132;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:133;a:5:{s:4:"time";d:1753196438.130682;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:134;a:5:{s:4:"time";d:1753196438.130717;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:135;a:5:{s:4:"time";d:1753196438.130787;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:136;a:5:{s:4:"time";d:1753196438.131297;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:137;a:5:{s:4:"time";d:1753196438.131313;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:138;a:5:{s:4:"time";d:1753196438.13135;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:139;a:5:{s:4:"time";d:1753196438.131761;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:140;a:5:{s:4:"time";d:1753196438.131774;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:141;a:5:{s:4:"time";d:1753196438.131804;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:142;a:5:{s:4:"time";d:1753196438.132247;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:143;a:5:{s:4:"time";d:1753196438.132266;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:144;a:5:{s:4:"time";d:1753196438.132306;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:145;a:5:{s:4:"time";d:1753196438.132797;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:146;a:5:{s:4:"time";d:1753196438.132822;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:147;a:5:{s:4:"time";d:1753196438.132861;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:148;a:5:{s:4:"time";d:1753196438.133168;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:149;a:5:{s:4:"time";d:1753196438.133179;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:150;a:5:{s:4:"time";d:1753196438.133204;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:151;a:5:{s:4:"time";d:1753196438.133533;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:152;a:5:{s:4:"time";d:1753196438.133543;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:153;a:5:{s:4:"time";d:1753196438.133566;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:154;a:5:{s:4:"time";d:1753196438.1341;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:155;a:5:{s:4:"time";d:1753196438.134111;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:156;a:5:{s:4:"time";d:1753196438.134134;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:157;a:5:{s:4:"time";d:1753196438.134435;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:158;a:5:{s:4:"time";d:1753196438.134447;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"common\models\DocumentType";}i:159;a:5:{s:4:"time";d:1753196438.136659;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:160;a:5:{s:4:"time";d:1753196438.14867;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:161;a:5:{s:4:"time";d:1753196438.149141;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:162;a:5:{s:4:"time";d:1753196438.166155;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:163;a:5:{s:4:"time";d:1753196438.168408;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:164;a:5:{s:4:"time";d:1753196438.170337;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:165;a:5:{s:4:"time";d:1753196438.170933;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:166;a:5:{s:4:"time";d:1753196438.172176;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:167;a:5:{s:4:"time";d:1753196438.173416;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:168;a:5:{s:4:"time";d:1753196438.174316;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:169;a:5:{s:4:"time";d:1753196438.175472;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:170;a:5:{s:4:"time";d:1753196438.175492;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"common\widgets\NotificationWidget";}i:171;a:5:{s:4:"time";d:1753196438.176385;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:172;a:5:{s:4:"time";d:1753196438.176399;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:173;a:5:{s:4:"time";d:1753196438.176651;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:174;a:5:{s:4:"time";d:1753196438.177466;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:175;a:5:{s:4:"time";d:1753196438.17748;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\ProfileWidget";}i:176;a:5:{s:4:"time";d:1753196438.177989;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:177;a:5:{s:4:"time";d:1753196438.177998;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:178;a:5:{s:4:"time";d:1753196438.17814;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:179;a:5:{s:4:"time";d:1753196438.188964;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:180;a:5:{s:4:"time";d:1753196438.189016;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:181;a:5:{s:4:"time";d:1753196438.189862;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"common\components\LucideSidebarMenuHelper";}i:182;a:5:{s:4:"time";d:1753196438.189901;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:183;a:5:{s:4:"time";d:1753196438.189927;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"common\widgets\SidebarWidget";}i:184;a:5:{s:4:"time";d:1753196438.192515;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:185;a:5:{s:4:"time";d:1753196438.192548;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:186;a:5:{s:4:"time";d:1753196438.192656;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:187;a:5:{s:4:"time";d:1753196438.194293;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"common\widgets\Alert";}i:188;a:5:{s:4:"time";d:1753196438.194315;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"common\widgets\Alert";}i:189;a:5:{s:4:"time";d:1753196438.194352;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"common\widgets\Alert";}i:190;a:5:{s:4:"time";d:1753196438.200513;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:191;a:5:{s:4:"time";d:1753196438.201082;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:192;a:5:{s:4:"time";d:1753196438.202002;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:193;a:5:{s:4:"time";d:1753196438.202059;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:39:"frontend\controllers\DocumentController";}i:194;a:5:{s:4:"time";d:1753196438.20305;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:195;a:5:{s:4:"time";d:1753196438.204127;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:196;a:5:{s:4:"time";d:1753196438.20414;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:197;a:5:{s:4:"time";d:1753196438.204145;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:198;a:5:{s:4:"time";d:1753196438.204576;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:199;a:5:{s:4:"time";d:1753196438.204752;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:1753196437.900417;s:3:"end";d:1753196438.205942;s:6:"memory";i:10409472;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:1277:"a:3:{s:8:"messages";a:6:{i:25;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753196438.042153;i:4;a:0:{}i:5;i:7063600;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753196438.042185;i:4;a:0:{}i:5;i:7064192;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753196438.042197;i:4;a:0:{}i:5;i:7064784;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753196438.042207;i:4;a:0:{}i:5;i:7065376;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753196438.042218;i:4;a:0:{}i:5;i:7065968;}i:30;a:6:{i:0;s:55:"No matching URL rules. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1753196438.042298;i:4;a:0:{}i:5;i:7066184;}}s:5:"route";s:17:"document/dossiers";s:6:"action";s:57:"frontend\controllers\DocumentController::actionDossiers()";}";s:7:"request";s:9430:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:19:{s:12:"content-type";s:0:"";s:14:"content-length";s:1:"0";s:14:"x-original-url";s:18:"/document/dossiers";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:25:"upgrade-insecure-requests";s:1:"1";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:65:""Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"";s:10:"user-agent";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:7:"referer";s:30:"http://localhost:8005/document";s:4:"host";s:14:"localhost:8005";s:6:"cookie";s:468:"advanced-frontend-fmz=i7ehtoqe5bs0p7rfn2tms6jvh9; _identity-frontend=20825fca1ba33f2e9fa7551c261673418774e609a156b6153cb0373705fbe6f1a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B7%2C%226126QpFT0R6UeNHmscglqzlP31CnBjEU%22%2C2592000%5D%22%3B%7D; _csrf-frontend=d6e888bdf57e1c528a486ad915aceae30aba144cd8dcda2a9caf47020b6bf6c6a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22MSzDQ-awuf-NBDccxti09-liOKU--En4%22%3B%7D";s:15:"accept-language";s:23:"en-US,en;q=0.9,nl;q=0.8";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"687fa7960837f";s:16:"X-Debug-Duration";s:3:"305";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=687fa7960837f";s:10:"Set-Cookie";s:313:"_identity-frontend=20825fca1ba33f2e9fa7551c261673418774e609a156b6153cb0373705fbe6f1a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B7%2C%226126QpFT0R6UeNHmscglqzlP31CnBjEU%22%2C2592000%5D%22%3B%7D; expires=Thu, 21 Aug 2025 15:00:38 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:17:"document/dossiers";s:6:"action";s:57:"frontend\controllers\DocumentController::actionDossiers()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:107:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-1a4b44dd-9e61-47ce-b1a8-5432625e3be7";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Reclassering\Reclassering.config";s:11:"APP_POOL_ID";s:12:"Reclassering";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:462:"C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:10:"/index.php";s:3:"URL";s:10:"/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"8005";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:10:"/index.php";s:15:"SCRIPT_FILENAME";s:42:"C:\Web\Reclassering\frontend\web\index.php";s:11:"REQUEST_URI";s:18:"/document/dossiers";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"51623";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:0:"";s:15:"PATH_TRANSLATED";s:42:"C:\Web\Reclassering\frontend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/2";s:13:"INSTANCE_NAME";s:12:"RECLASSERING";s:11:"INSTANCE_ID";s:1:"2";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\Reclassering\frontend\web";s:12:"CONTENT_TYPE";s:0:"";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:33:"C:\Web\Reclassering\frontend\web\";s:12:"APPL_MD_PATH";s:16:"/LM/W3SVC/2/ROOT";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:13:"UNENCODED_URL";s:18:"/document/dossiers";s:19:"IIS_WasUrlRewritten";s:1:"1";s:19:"HTTP_X_ORIGINAL_URL";s:18:"/document/dossiers";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:65:""Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"";s:15:"HTTP_USER_AGENT";s:125:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********";s:12:"HTTP_REFERER";s:30:"http://localhost:8005/document";s:9:"HTTP_HOST";s:14:"localhost:8005";s:11:"HTTP_COOKIE";s:468:"advanced-frontend-fmz=i7ehtoqe5bs0p7rfn2tms6jvh9; _identity-frontend=20825fca1ba33f2e9fa7551c261673418774e609a156b6153cb0373705fbe6f1a%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B7%2C%226126QpFT0R6UeNHmscglqzlP31CnBjEU%22%2C2592000%5D%22%3B%7D; _csrf-frontend=d6e888bdf57e1c528a486ad915aceae30aba144cd8dcda2a9caf47020b6bf6c6a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22MSzDQ-awuf-NBDccxti09-liOKU--En4%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:23:"en-US,en;q=0.9,nl;q=0.8";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:19:"HTTP_CONTENT_LENGTH";s:1:"0";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1753196437.887106;s:12:"REQUEST_TIME";i:1753196437;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:3:{s:21:"advanced-frontend-fmz";s:26:"i7ehtoqe5bs0p7rfn2tms6jvh9";s:18:"_identity-frontend";s:158:"20825fca1ba33f2e9fa7551c261673418774e609a156b6153cb0373705fbe6f1a:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[7,"6126QpFT0R6UeNHmscglqzlP31CnBjEU",2592000]";}";s:14:"_csrf-frontend";s:140:"d6e888bdf57e1c528a486ad915aceae30aba144cd8dcda2a9caf47020b6bf6c6a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"MSzDQ-awuf-NBDccxti09-liOKU--En4";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:4:{s:7:"__flash";a:0:{}s:11:"__returnUrl";s:22:"http://localhost:8005/";s:4:"__id";i:7;s:9:"__authKey";s:32:"6126QpFT0R6UeNHmscglqzlP31CnBjEU";}}";s:4:"user";s:1448:"a:5:{s:2:"id";i:7;s:8:"identity";a:12:{s:2:"id";s:1:"7";s:8:"username";s:12:"'medewerker'";s:8:"auth_key";s:34:"'6126QpFT0R6UeNHmscglqzlP31CnBjEU'";s:13:"password_hash";s:62:"'$2y$13$0DIFqBhjLTVgZctJptjzeOjd3WWC31ECxIYfpDBiaO7VtCzqVfuVC'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:20:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1743699359";s:10:"updated_at";s:10:"1749726911";s:18:"verification_token";s:4:"null";s:7:"role_id";s:1:"4";s:12:"access_token";s:4:"null";}s:10:"attributes";a:12:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:8:"Password";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:4:"Role";}i:11;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:4655:"a:11:{s:76:"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:32:"C:\Web\Reclassering\frontend\web";s:7:"baseUrl";s:0:"";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;a:1:{i:0;s:76:"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js";}}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:50:"C:\Web\Reclassering/vendor/bower-asset/jquery/dist";s:8:"basePath";s:48:"C:\Web\Reclassering\frontend\web\assets\8bc86ca8";s:7:"baseUrl";s:16:"/assets/8bc86ca8";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:41:"hail812\adminlte3\assets\FontAwesomeAsset";a:9:{s:10:"sourcePath";s:74:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/plugins/fontawesome-free";s:8:"basePath";s:48:"C:\Web\Reclassering\frontend\web\assets\9a5ee6fe";s:7:"baseUrl";s:16:"/assets/9a5ee6fe";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:15:"css/all.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:38:"hail812\adminlte3\assets\AdminLteAsset";a:9:{s:10:"sourcePath";s:54:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/dist";s:8:"basePath";s:48:"C:\Web\Reclassering\frontend\web\assets\f545690b";s:7:"baseUrl";s:16:"/assets/f545690b";s:7:"depends";a:2:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";i:1;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:1:{i:0;s:18:"js/adminlte.min.js";}s:3:"css";a:1:{i:0;s:20:"css/adminlte.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:34:"hail812\adminlte3\assets\BaseAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";N;s:7:"baseUrl";N;s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";i:2;s:35:"yii\bootstrap4\BootstrapPluginAsset";}s:2:"js";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\Reclassering\vendor\yiisoft\yii2/assets";s:8:"basePath";s:48:"C:\Web\Reclassering\frontend\web\assets\ea09678e";s:7:"baseUrl";s:16:"/assets/ea09678e";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap4\BootstrapAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:48:"C:\Web\Reclassering\frontend\web\assets\75eeaf84";s:7:"baseUrl";s:16:"/assets/75eeaf84";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap4\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\Reclassering/vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:48:"C:\Web\Reclassering\frontend\web\assets\75eeaf84";s:7:"baseUrl";s:16:"/assets/75eeaf84";s:7:"depends";a:2:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:36:"hail812\adminlte3\assets\PluginAsset";a:9:{s:10:"sourcePath";s:57:"C:\Web\Reclassering/vendor/almasaeed2010/adminlte/plugins";s:8:"basePath";s:48:"C:\Web\Reclassering\frontend\web\assets\834b4d89";s:7:"baseUrl";s:16:"/assets/834b4d89";s:7:"depends";a:1:{i:0;s:34:"hail812\adminlte3\assets\BaseAsset";}s:2:"js";a:0:{}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:24:"frontend\assets\AppAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:32:"C:\Web\Reclassering\frontend\web";s:7:"baseUrl";s:0:"";s:7:"depends";a:4:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:38:"hail812\adminlte3\assets\AdminLteAsset";i:3;s:36:"hail812\adminlte3\assets\PluginAsset";}s:2:"js";a:2:{i:0;s:50:"https://unpkg.com/lucide@latest/dist/umd/lucide.js";i:1;s:10:"js/main.js";}s:3:"css";a:1:{i:0;s:12:"css/site.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap5\BootstrapAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\Reclassering/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:48:"C:\Web\Reclassering\frontend\web\assets\4ceb4c69";s:7:"baseUrl";s:16:"/assets/4ceb4c69";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"687fa7960837f";s:3:"url";s:39:"http://localhost:8005/document/dossiers";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:1753196437.887106;s:10:"statusCode";i:200;s:8:"sqlCount";i:52;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10409472;s:14:"processingTime";d:0.30496883392333984;}s:10:"exceptions";a:0:{}}